from typing import Optional
from passlib.context import Crypt<PERSON>ontext
from app.core.app_database import get_app_database_service

# Password hashing context (consistent with sync script)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User:
    """User model for FastAPI authentication"""

    def __init__(self, id: int, username: str, password_hash: str, role: str,
                 first_name: str = "", last_name: str = "", department_name: str = ""):
        self.id = id
        self.username = username
        self.password_hash = password_hash  # Store bcrypt hashed passwords
        self.role = role
        self.first_name = first_name
        self.last_name = last_name
        self.department_name = department_name

    def get_id(self):
        return str(self.id)

    @property
    def full_name(self):
        """Get the user's full name"""
        return f"{self.first_name} {self.last_name}".strip() or self.username

    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using bcrypt"""
        return pwd_context.hash(password)

    def check_password(self, password: str) -> bool:
        """Check if provided password matches the stored bcrypt hash"""
        return pwd_context.verify(password, self.password_hash)

def get_user_by_id(user_id: str) -> Optional[User]:
    """Get user by ID from the app database"""
    try:
        db_service = get_app_database_service()
        user_data = db_service.get_user_by_id(int(user_id))

        if user_data:
            return User(
                id=user_data['id'],
                username=user_data['username'],
                password_hash=user_data['password_hash'],
                role=user_data['role_name'],
                first_name=user_data['first_name'] or '',
                last_name=user_data['last_name'] or '',
                department_name=user_data['department_name'] or ''
            )
        return None
    except Exception as e:
        print(f"Error getting user by ID {user_id}: {e}")
        return None

def get_user_by_username(username: str) -> Optional[User]:
    """Get user by username from the app database"""
    try:
        db_service = get_app_database_service()
        user_data = db_service.get_user_by_username(username)

        if user_data:
            return User(
                id=user_data['id'],
                username=user_data['username'],
                password_hash=user_data['password_hash'],
                role=user_data['role_name'],
                first_name=user_data['first_name'] or '',
                last_name=user_data['last_name'] or '',
                department_name=user_data['department_name'] or ''
            )
        return None
    except Exception as e:
        print(f"Error getting user by username {username}: {e}")
        return None
