# Hospital Assistant System - React Frontend

This is the React frontend for the Hospital Assistant System, converted from static HTML files to a modern React application with Tailwind CSS.

## Features

- **Modern React 18** with functional components and hooks
- **Tailwind CSS** for responsive, utility-first styling
- **Dark/Light Theme** support with system preference detection
- **Real-time Chat Interface** with streaming responses
- **Interactive Dashboard** with charts and statistics
- **Patient Search** functionality

- **Settings Management** with user preferences
- **Authentication** with protected routes
- **Responsive Design** optimized for mobile and desktop

## Project Structure

```
frontend/
├── public/
│   └── index.html          # Main HTML template
├── src/
│   ├── components/
│   │   ├── Auth/           # Authentication components
│   │   ├── Chat/           # Chat interface components
│   │   ├── Dashboard/      # Dashboard widgets and stats
│   │   ├── Layout/         # Layout components (Header, Sidebar, Footer)
│   │   └── UI/             # Reusable UI components
│   ├── contexts/           # React contexts (Theme, Auth)
│   ├── pages/              # Main page components
│   ├── App.jsx             # Main app component
│   ├── index.js            # Entry point
│   └── index.css           # Global styles and CSS variables
├── package.json            # Dependencies and scripts
├── tailwind.config.js      # Tailwind CSS configuration
└── postcss.config.js       # PostCSS configuration
```

## Components Overview

### Pages
- **Login** - Authentication page with demo accounts
- **Dashboard** - System overview with charts and statistics
- **Chatbot** - AI chat interface with streaming responses
- **PatientSearch** - Search patients by various criteria

- **Settings** - User preferences and system settings

### Key Components
- **ChatMessage** - Individual chat message with SQL display and charts
- **ChatInput** - Message input with auto-resize and keyboard shortcuts
- **DataTable** - Responsive table for displaying query results
- **Chart** - Plotly.js integration with theme support
- **StatCard** - Dashboard statistics cards
- **Widget** - Dashboard widget container with loading states

## Installation

1. **Navigate to the frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

The application will open at `http://localhost:3000` and proxy API requests to the FastAPI backend at `http://localhost:8000`.

## Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm run eject` - Eject from Create React App (not recommended)

## Configuration

### Theme System
The application supports both light and dark themes with:
- System preference detection
- Manual theme switching
- CSS custom properties for consistent theming
- Plotly chart theme integration

### API Integration
- Configured to proxy requests to FastAPI backend
- Real-time streaming with Server-Sent Events
- Error handling and loading states
- Authentication state management

### Responsive Design
- Mobile-first approach with Tailwind CSS
- Collapsible sidebar for mobile devices
- Responsive charts and tables
- Touch-friendly interface elements

## Key Features Converted from Original HTML

### Chat Interface (index.html)
- ✅ Real-time streaming chat with thinking states
- ✅ SQL query display with collapsible LLM output
- ✅ Interactive charts with Plotly.js
- ✅ Data tables with responsive design
- ✅ Loading animations and error handling

### Dashboard (dashboard.html)
- ✅ Statistics cards with hover effects
- ✅ Widget-based layout with loading states
- ✅ Chart integration with theme support
- ✅ Responsive grid layout

### Authentication (login.html)
- ✅ Login form with validation
- ✅ Demo account information
- ✅ Theme toggle on login page
- ✅ Protected route implementation

### Navigation & Layout
- ✅ Responsive sidebar with mobile menu
- ✅ Header with user info and theme toggle
- ✅ Footer with links
- ✅ Consistent styling across all pages

## Styling Approach

The application uses a hybrid approach:
- **Tailwind CSS** for utility-first styling and responsive design
- **CSS Custom Properties** for theme variables (maintaining original color scheme)
- **Custom CSS** for animations and complex interactions
- **Component-scoped styles** for specific functionality

## Browser Support

- Modern browsers with ES6+ support
- Chrome, Firefox, Safari, Edge
- Mobile browsers (iOS Safari, Chrome Mobile)

## Development Notes

- Uses React 18 with concurrent features
- Functional components with hooks throughout
- Context API for global state management
- Error boundaries for graceful error handling
- Accessibility considerations with ARIA labels

## Production Build

To create a production build:

```bash
npm run build
```

This creates an optimized build in the `build/` directory ready for deployment.

## Integration with Backend

The frontend is designed to work seamlessly with the FastAPI backend:
- Authentication endpoints
- Real-time chat streaming
- Dashboard data APIs
- Patient search functionality

- Settings management

Make sure the FastAPI backend is running on port 8000 for full functionality.
