import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTheme } from '../../contexts/ThemeContext';

const Modal = ({
  isOpen,
  onClose,
  children,
  title,
  originPosition = null,
  originElement = null,
  size = 'medium' // 'small', 'medium', 'large'
}) => {
  const { isDark } = useTheme();
  const modalRef = useRef(null);
  const backdropRef = useRef(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationPhase, setAnimationPhase] = useState('closed'); // 'closed', 'opening', 'open', 'closing'

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen && animationPhase === 'open') {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, animationPhase]); // handleClose is defined inline, so it's safe to omit

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === backdropRef.current && animationPhase === 'open') {
      handleClose();
    }
  };

  // Handle close with animation
  const handleClose = () => {
    if (animationPhase === 'open') {
      setAnimationPhase('closing');
      setIsAnimating(true);
    }
  };

  // Morphing animation effect
  useEffect(() => {
    if (isOpen && !isAnimating) {
      setAnimationPhase('opening');
      setIsAnimating(true);
    } else if (!isOpen && animationPhase === 'closing') {
      // Animation completed, reset state
      const timer = setTimeout(() => {
        setAnimationPhase('closed');
        setIsAnimating(false);
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isOpen, animationPhase, isAnimating]);

  // Handle opening animation
  useEffect(() => {
    if (animationPhase === 'opening' && modalRef.current && originPosition && originElement) {
      const modal = modalRef.current;
      const originRect = originElement.getBoundingClientRect();

      // Add morphing effect to original widget
      if (originElement) {
        originElement.classList.add('widget-morphing', 'animating');
        originElement.style.opacity = '0.8';
      }

      // Set initial state to match the widget
      modal.style.position = 'fixed';
      modal.style.top = `${originRect.top}px`;
      modal.style.left = `${originRect.left}px`;
      modal.style.width = `${originRect.width}px`;
      modal.style.height = `${originRect.height}px`;
      modal.style.transform = 'none';
      modal.style.borderRadius = '0.75rem'; // Match widget border radius
      modal.style.transition = 'none';
      modal.style.opacity = '1';

      // Trigger morphing animation
      requestAnimationFrame(() => {
        // Improved animation with smoother easing and longer duration
        modal.style.transition = 'all 0.6s cubic-bezier(0.16, 1, 0.3, 1), transform 0.6s cubic-bezier(0.16, 1, 0.3, 1)';

        // Calculate final modal position (centered)
        const finalWidth = Math.min(window.innerWidth * 0.9, 1024); // max-w-4xl equivalent
        const finalHeight = Math.min(window.innerHeight * 0.85, 700); // Increased height for better content display
        const finalLeft = (window.innerWidth - finalWidth) / 2;
        const finalTop = (window.innerHeight - finalHeight) / 2;

        // Animate to final modal state with subtle scaling effect
        modal.style.top = `${finalTop}px`;
        modal.style.left = `${finalLeft}px`;
        modal.style.width = `${finalWidth}px`;
        modal.style.height = `${finalHeight}px`;
        modal.style.borderRadius = '0.75rem';
        modal.style.transform = 'scale(1)'; // Ensure final scale is 1
        modal.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)'; // Enhanced shadow

        // Complete opening animation
        const timer = setTimeout(() => {
          setAnimationPhase('open');
        }, 600); // Increased to match new duration

        return () => clearTimeout(timer);
      });
    }
  }, [animationPhase, originPosition, originElement]);

  // Handle closing animation
  useEffect(() => {
    if (animationPhase === 'closing' && modalRef.current && originPosition && originElement) {
      const modal = modalRef.current;
      const originRect = originElement.getBoundingClientRect();

      // Animate back to widget position with improved easing
      modal.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1), transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
      modal.style.top = `${originRect.top}px`;
      modal.style.left = `${originRect.left}px`;
      modal.style.width = `${originRect.width}px`;
      modal.style.height = `${originRect.height}px`;
      modal.style.borderRadius = '0.75rem';
      modal.style.transform = 'scale(0.95)'; // Slight scale down for smooth transition
      modal.style.opacity = '0.8'; // Fade out slightly

      // Complete closing animation
      const timer = setTimeout(() => {
        // Restore original widget
        if (originElement) {
          originElement.style.opacity = '1';
          originElement.classList.remove('widget-morphing', 'animating');
        }
        onClose();
      }, 500); // Increased to match new duration

      return () => clearTimeout(timer);
    }
  }, [animationPhase, originPosition, originElement, onClose]);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-full max-w-md max-h-[50vh]';
      case 'large':
        return 'w-full max-w-6xl max-h-[90vh]';
      case 'medium':
      default:
        return 'w-full max-w-4xl max-h-[80vh]';
    }
  };

  if (!isOpen && animationPhase === 'closed') return null;

  const modalContent = (
    <div
      ref={backdropRef}
      className="fixed inset-0 z-50"
      onClick={handleBackdropClick}
      style={{
        backgroundColor: animationPhase === 'open'
          ? (isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)')
          : 'transparent',
        backdropFilter: animationPhase === 'open' ? 'blur(6px)' : 'none',
        transition: 'background-color 0.6s ease, backdrop-filter 0.6s ease',
        pointerEvents: animationPhase === 'open' ? 'auto' : 'none'
      }}
    >
      <div
        ref={modalRef}
        className={`
          bg-white dark:bg-gray-800 shadow-2xl
          overflow-hidden
          border border-gray-200 dark:border-gray-600
          ring-1 ring-black ring-opacity-5 dark:ring-white dark:ring-opacity-10
        `}
        style={{
          position: 'fixed',
          zIndex: 60,
          boxShadow: isDark
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'
            : '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div
          className="flex items-center justify-between px-8 py-6 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800"
          style={{
            opacity: animationPhase === 'open' ? 1 : 0,
            transition: 'opacity 0.4s ease',
            transitionDelay: animationPhase === 'opening' ? '0.3s' : '0s'
          }}
        >
          <div className="flex items-center">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h2>
            <div className="ml-3 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
              Search
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-all duration-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 hover:scale-110"
            aria-label="Close modal"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Content */}
        <div
          className="overflow-y-auto bg-white dark:bg-gray-800"
          style={{
            opacity: animationPhase === 'open' ? 1 : 0,
            transition: 'opacity 0.4s ease',
            transitionDelay: animationPhase === 'opening' ? '0.3s' : '0s',
            height: 'calc(100% - 88px)' // Account for header height
          }}
        >
          <div className="p-8">
            {/* Search Instructions */}
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Search Tips
                  </h3>
                  <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    Use the dropdown to select your search criteria, then enter your search term. Results will appear below.
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="space-y-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default Modal;
