import React from 'react';
import LoadingSpinner from '../UI/LoadingSpinner';

const Widget = ({ title, icon, children, loading = false, error = null, className = '' }) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300 ${className}`}>
      {/* Widget Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          {icon && <i className={`${icon} text-blue-600 dark:text-blue-400 mr-3`}></i>}
          {title}
        </h3>
      </div>

      {/* Widget Content */}
      <div className="p-6">
        {loading && (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" text={`Loading ${title.toLowerCase()}...`} />
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-64 text-red-500 dark:text-red-400">
            <div className="text-center">
              <i className="fas fa-exclamation-triangle text-3xl mb-4"></i>
              <p className="text-lg font-medium mb-2">Error Loading Data</p>
              <p className="text-sm opacity-80">{error}</p>
            </div>
          </div>
        )}

        {!loading && !error && children}
      </div>
    </div>
  );
};

export default Widget;
