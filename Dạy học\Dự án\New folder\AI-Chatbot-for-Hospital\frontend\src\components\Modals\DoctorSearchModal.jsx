import React, { useState } from 'react';
import LoadingSpinner from '../UI/LoadingSpinner';
import DoctorDetailsModal from './DoctorDetailsModal';
import PatientDetailsModal from './PatientDetailsModal';
import ConsultationDetailsModal from './ConsultationDetailsModal';

const DoctorSearchModal = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('name');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedDoctorId, setSelectedDoctorId] = useState(null);
  const [showDoctorDetails, setShowDoctorDetails] = useState(false);
  const [selectedPatientId, setSelectedPatientId] = useState(null);
  const [showPatientDetails, setShowPatientDetails] = useState(false);
  const [selectedConsultationId, setSelectedConsultationId] = useState(null);
  const [showConsultationDetails, setShowConsultationDetails] = useState(false);

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) {
      setError('Please enter a search term');
      return;
    }

    setLoading(true);
    setError('');
    setHasSearched(true);

    try {
      const response = await fetch('/api/v1/search/doctors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: searchQuery.trim(),
          type: searchType
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data.results || []);
      } else {
        setError(data.error || 'Search failed. Please try again.');
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setError('Network error. Please check your connection and try again.');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDoctorClick = (doctor) => {
    setSelectedDoctorId(doctor.doctor_id);
    setShowDoctorDetails(true);
  };

  const handleCloseDoctorDetails = () => {
    setShowDoctorDetails(false);
    setSelectedDoctorId(null);
  };

  const handleClosePatientDetails = () => {
    setShowPatientDetails(false);
    setSelectedPatientId(null);
  };

  const handleCloseConsultationDetails = () => {
    setShowConsultationDetails(false);
    setSelectedConsultationId(null);
  };

  // Cross-modal navigation handlers
  const handlePatientClickFromDoctor = (patientId) => {
    setShowDoctorDetails(false);
    setSelectedPatientId(patientId);
    setShowPatientDetails(true);
  };

  const handleConsultationClickFromDoctor = (consultationId) => {
    setShowDoctorDetails(false);
    setSelectedConsultationId(consultationId);
    setShowConsultationDetails(true);
  };

  const handlePatientClickFromConsultation = (patientId) => {
    setShowConsultationDetails(false);
    setSelectedPatientId(patientId);
    setShowPatientDetails(true);
  };

  const handleDoctorClickFromConsultation = (doctorId) => {
    setShowConsultationDetails(false);
    setSelectedDoctorId(doctorId);
    setShowDoctorDetails(true);
  };

  return (
    <div>
      {/* Search Form */}
      <div className="mb-8">
        <form onSubmit={handleSearch} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Search Type */}
            <div>
              <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                Search By
              </label>
              <select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
              >
                <option value="name">Doctor Name</option>
                <option value="specialty">Specialty</option>
                <option value="department">Department</option>
                <option value="email">Email</option>
                <option value="doctor_id">Doctor ID</option>
              </select>
            </div>

            {/* Search Query */}
            <div>
              <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                Search Term
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={`Enter ${searchType === 'name' ? 'doctor name' : searchType}...`}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
              />
            </div>
          </div>

          {/* Search Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              disabled={loading}
              className="px-8 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold rounded-lg transition-all duration-200 flex items-center text-base shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-3"></i>
                  Searching...
                </>
              ) : (
                <>
                  <i className="fas fa-search mr-3"></i>
                  Search Doctors
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <i className="fas fa-exclamation-triangle text-red-500 mr-3 text-base"></i>
            <p className="text-base text-red-700 dark:text-red-400">{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" text="Searching doctors..." />
        </div>
      )}

      {/* Results */}
      {!loading && hasSearched && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Search Results
              {results.length > 0 && (
                <span className="ml-3 text-base font-normal text-gray-500 dark:text-gray-400">
                  ({results.length} doctor{results.length !== 1 ? 's' : ''} found)
                </span>
              )}
            </h3>
          </div>

          {results.length === 0 ? (
            <div className="p-12 text-center">
              <i className="fas fa-search text-4xl text-gray-400 mb-4"></i>
              <p className="text-lg text-gray-500 dark:text-gray-400 mb-2">No doctors found</p>
              <p className="text-base text-gray-400 dark:text-gray-500">
                Try adjusting your search criteria or search terms
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto rounded-lg">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Doctor ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Specialty
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {results.map((doctor, index) => (
                    <tr
                      key={index}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                      onClick={() => handleDoctorClick(doctor)}
                      title="Click to view doctor details"
                    >
                      <td className="px-3 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        {doctor.doctor_id || 'N/A'}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          <div className="font-medium">{doctor.first_name} {doctor.last_name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Dr. {doctor.last_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {doctor.specialty || 'N/A'}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {doctor.department || 'N/A'}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          {doctor.phone && (
                            <div className="text-xs">
                              <i className="fas fa-phone mr-1"></i>
                              {doctor.phone}
                            </div>
                          )}
                          {doctor.email && (
                            <div className="text-xs">
                              <i className="fas fa-envelope mr-1"></i>
                              {doctor.email}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          doctor.status === 'active' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : doctor.status === 'inactive'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {doctor.status || 'Unknown'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Doctor Details Modal */}
      <DoctorDetailsModal
        isOpen={showDoctorDetails}
        onClose={handleCloseDoctorDetails}
        doctorId={selectedDoctorId}
        onPatientClick={handlePatientClickFromDoctor}
        onConsultationClick={handleConsultationClickFromDoctor}
      />

      {/* Patient Details Modal */}
      <PatientDetailsModal
        isOpen={showPatientDetails}
        onClose={handleClosePatientDetails}
        patientId={selectedPatientId}
      />

      {/* Consultation Details Modal */}
      <ConsultationDetailsModal
        isOpen={showConsultationDetails}
        onClose={handleCloseConsultationDetails}
        consultationId={selectedConsultationId}
        onPatientClick={handlePatientClickFromConsultation}
        onDoctorClick={handleDoctorClickFromConsultation}
      />
    </div>
  );
};

export default DoctorSearchModal;
