import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

const ChatContext = createContext();

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

export const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentVannaId, setCurrentVannaId] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSwitchingChat, setIsSwitchingChat] = useState(false);
  const currentEventSourceRef = useRef(null);
  const isJustSwitchingRef = useRef(false);

  // Chat Sessions Management
  const [chatSessions, setChatSessions] = useState({});
  const [currentSessionId, setCurrentSessionId] = useState(null);
  const [sessionCounter, setSessionCounter] = useState(1);

  // Load chat sessions from localStorage on mount
  useEffect(() => {
    const savedSessions = localStorage.getItem('chatSessions');
    const savedCurrentSessionId = localStorage.getItem('currentSessionId');
    const savedSessionCounter = localStorage.getItem('sessionCounter');

    if (savedSessions) {
      try {
        const parsedSessions = JSON.parse(savedSessions);
        // Convert timestamp strings back to Date objects for all sessions
        const sessionsWithDates = {};
        Object.keys(parsedSessions).forEach(sessionId => {
          sessionsWithDates[sessionId] = {
            ...parsedSessions[sessionId],
            messages: parsedSessions[sessionId].messages.map(msg => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            })),
            createdAt: new Date(parsedSessions[sessionId].createdAt),
            updatedAt: new Date(parsedSessions[sessionId].updatedAt)
          };
        });
        setChatSessions(sessionsWithDates);

        // Load current session if it exists
        if (savedCurrentSessionId && sessionsWithDates[savedCurrentSessionId]) {
          setCurrentSessionId(savedCurrentSessionId);
          setMessages(sessionsWithDates[savedCurrentSessionId].messages);
          setCurrentVannaId(sessionsWithDates[savedCurrentSessionId].vannaId);
        } else {
          // Create a new session if none exists
          createNewSession();
        }
      } catch (error) {
        console.error('Error parsing saved chat sessions:', error);
        createNewSession();
      }
    } else {
      // No saved sessions, create first session
      createNewSession();
    }

    if (savedSessionCounter) {
      setSessionCounter(parseInt(savedSessionCounter, 10));
    }
  }, []);

  // Save chat sessions to localStorage whenever they change
  useEffect(() => {
    if (Object.keys(chatSessions).length > 0) {
      localStorage.setItem('chatSessions', JSON.stringify(chatSessions));
    }
  }, [chatSessions]);

  // Save current session ID to localStorage whenever it changes
  useEffect(() => {
    if (currentSessionId) {
      localStorage.setItem('currentSessionId', currentSessionId);
    }
  }, [currentSessionId]);

  // Save session counter to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sessionCounter', sessionCounter.toString());
  }, [sessionCounter]);

  // Update current session when messages change
  useEffect(() => {
    if (currentSessionId && messages.length > 0) {
      setChatSessions(prev => {
        const currentSession = prev[currentSessionId];
        // Don't update updatedAt if we're just switching between chats
        // Only update when new messages are actually added
        const shouldUpdateTimestamp = !isJustSwitchingRef.current &&
          (!currentSession || currentSession.messages.length !== messages.length);

        return {
          ...prev,
          [currentSessionId]: {
            ...currentSession,
            messages: messages,
            updatedAt: shouldUpdateTimestamp ? new Date() : currentSession.updatedAt,
            vannaId: currentVannaId
          }
        };
      });

      // Reset the switching flag after updating
      isJustSwitchingRef.current = false;
    }
  }, [messages, currentSessionId, currentVannaId]);

  const initializeWithWelcomeMessage = () => {
    const welcomeMessage = {
      id: 'welcome',
      type: 'bot',
      content: "Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data,... or any other database information.",
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  // Session Management Functions
  const createNewSession = (title = null) => {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sessionTitle = title || `Chat ${sessionCounter}`;
    const now = new Date();

    const welcomeMessage = {
      id: 'welcome',
      type: 'bot',
      content: "Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data,... or any other database information.",
      timestamp: now
    };

    const newSession = {
      id: sessionId,
      title: sessionTitle,
      messages: [welcomeMessage],
      createdAt: now,
      updatedAt: now,
      vannaId: null
    };

    setChatSessions(prev => ({
      ...prev,
      [sessionId]: newSession
    }));

    setCurrentSessionId(sessionId);
    setMessages([welcomeMessage]);
    setCurrentVannaId(null);
    setSessionCounter(prev => prev + 1);

    return sessionId;
  };

  const switchToSession = async (sessionId) => {
    if (chatSessions[sessionId] && sessionId !== currentSessionId) {
      setIsSwitchingChat(true);

      // Small delay to show loading state and create smooth transition
      await new Promise(resolve => setTimeout(resolve, 150));

      // Set flag to indicate we're just switching, not adding new content
      isJustSwitchingRef.current = true;

      // Switch to the session without updating its updatedAt timestamp
      // This prevents the chat from moving to the top of the history
      setCurrentSessionId(sessionId);
      setMessages([...chatSessions[sessionId].messages]); // Create a copy to avoid reference issues
      setCurrentVannaId(chatSessions[sessionId].vannaId);

      // Brief delay before removing loading state
      setTimeout(() => {
        setIsSwitchingChat(false);
      }, 100);
    }
  };

  const deleteSession = (sessionId) => {
    if (Object.keys(chatSessions).length <= 1) {
      // Don't delete the last session, create a new one instead
      createNewSession();
      return;
    }

    setChatSessions(prev => {
      const newSessions = { ...prev };
      delete newSessions[sessionId];
      return newSessions;
    });

    // If we're deleting the current session, switch to another one
    if (currentSessionId === sessionId) {
      const remainingSessions = Object.keys(chatSessions).filter(id => id !== sessionId);
      if (remainingSessions.length > 0) {
        switchToSession(remainingSessions[0]);
      } else {
        createNewSession();
      }
    }
  };

  const renameSession = (sessionId, newTitle) => {
    setChatSessions(prev => ({
      ...prev,
      [sessionId]: {
        ...prev[sessionId],
        title: newTitle,
        updatedAt: new Date()
      }
    }));
  };

  const duplicateSession = (sessionId) => {
    if (chatSessions[sessionId]) {
      const originalSession = chatSessions[sessionId];
      const newSessionId = createNewSession(`${originalSession.title} (Copy)`);

      // Copy messages from original session
      setChatSessions(prev => ({
        ...prev,
        [newSessionId]: {
          ...prev[newSessionId],
          messages: [...originalSession.messages]
        }
      }));

      setMessages([...originalSession.messages]);
      return newSessionId;
    }
  };

  const addMessage = (content, type, additionalData = {}) => {
    const message = {
      id: Date.now() + Math.random(),
      type,
      content,
      timestamp: new Date(),
      ...additionalData
    };
    
    setMessages(prev => [...prev, message]);
    return message;
  };

  const updateLastMessage = (updates) => {
    setMessages(prev => {
      const newMessages = [...prev];
      const lastIndex = newMessages.length - 1;
      if (lastIndex >= 0) {
        newMessages[lastIndex] = { ...newMessages[lastIndex], ...updates };
      }
      return newMessages;
    });
  };

  const updateMessage = (messageId, updates) => {
    setMessages(prev => {
      return prev.map(message =>
        message.id === messageId
          ? { ...message, ...updates }
          : message
      );
    });
  };

  const clearChatHistory = () => {
    // Clear current session only
    if (currentSessionId) {
      const welcomeMessage = {
        id: 'welcome',
        type: 'bot',
        content: "Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data,... or any other database information.",
        timestamp: new Date()
      };

      setMessages([welcomeMessage]);
      setCurrentVannaId(null);

      setChatSessions(prev => ({
        ...prev,
        [currentSessionId]: {
          ...prev[currentSessionId],
          messages: [welcomeMessage],
          vannaId: null,
          updatedAt: new Date()
        }
      }));
    }
  };

  const clearAllSessions = () => {
    localStorage.removeItem('chatSessions');
    localStorage.removeItem('currentSessionId');
    localStorage.removeItem('sessionCounter');
    setChatSessions({});
    setCurrentSessionId(null);
    setCurrentVannaId(null);
    setSessionCounter(1);
    createNewSession();
  };

  const value = {
    // State
    messages,
    isLoading,
    currentVannaId,
    isProcessing,
    isSwitchingChat,
    currentEventSourceRef,

    // Chat Sessions State
    chatSessions,
    currentSessionId,
    sessionCounter,

    // State setters
    setMessages,
    setIsLoading,
    setCurrentVannaId,
    setIsProcessing,
    setIsSwitchingChat,

    // Methods
    addMessage,
    updateLastMessage,
    updateMessage,
    clearChatHistory,

    // Session Management Methods
    createNewSession,
    switchToSession,
    deleteSession,
    renameSession,
    duplicateSession,
    clearAllSessions
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};
