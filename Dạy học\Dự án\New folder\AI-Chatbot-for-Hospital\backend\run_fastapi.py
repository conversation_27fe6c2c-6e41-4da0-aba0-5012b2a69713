"""
FastAPI startup script for the Hospital Assistant System
"""

import uvicorn
from config import Config

if __name__ == "__main__":
    # Load configuration
    config = Config()
    
    print("🚀 Starting Hospital Assistant System with FastAPI...")
    print(f"🌐 Server will be available at: http://localhost:8000")
    print(f"📚 API documentation at: http://localhost:8000/docs")
    print(f"🔧 Debug mode: {config.DEBUG}")
    print("=" * 60)
    
    # Run the FastAPI application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=config.DEBUG,
        log_level="info",
        access_log=True
    )
