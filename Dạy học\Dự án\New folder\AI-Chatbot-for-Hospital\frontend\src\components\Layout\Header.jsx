import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';

const Header = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="bg-white dark:bg-gray-800 px-6 py-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700 shadow-sm transition-colors duration-300">
      <span className="text-xl font-semibold text-gray-900 dark:text-gray-100">
        HOSPITAL ASSISTANT SYSTEM
      </span>
      
      <div className="flex items-center space-x-5">
        {/* Language Options */}
        <div className="hidden md:flex items-center space-x-2 text-sm">
          <a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            <i className="fas fa-globe mr-1"></i> EN
          </a>
          <span className="text-gray-400">|</span>
          <a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            <i className="fas fa-globe mr-1"></i> FR
          </a>
        </div>

        {/* User Info */}
        <span className="text-gray-700 dark:text-gray-300 text-sm">
          <i className="fas fa-user-md mr-2"></i>
          {user?.name || 'Dr. Leblanc'}
        </span>

        {/* Logout */}
        <button
          onClick={handleLogout}
          className="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors text-sm"
        >
          <i className="fas fa-sign-out-alt mr-1"></i>
          Logout
        </button>

        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className="flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm"
        >
          <i className={`fas ${theme === 'dark' ? 'fa-sun' : 'fa-moon'} mr-2`}></i>
          <span className="hidden sm:inline">Toggle Theme</span>
        </button>
      </div>
    </div>
  );
};

export default Header;
