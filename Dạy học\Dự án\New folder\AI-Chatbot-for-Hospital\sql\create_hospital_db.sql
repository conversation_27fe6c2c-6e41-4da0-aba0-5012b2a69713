-- Patients table
CREATE TABLE Patients (
    ID_patient SERIAL PRIMARY KEY,
    Nom VARCHAR(100) NOT NULL,
    Prenom VARCHAR(100),
    DateNaissance DATE,
    Sexe CHAR(1),
    Adresse VARCHAR(255),
    Telephone VARCHAR(20),
    Email VARCHAR(100)
);

-- Medecins table
CREATE TABLE Medecins (
    ID_medecin SERIAL PRIMARY KEY,
    Nom VARCHAR(100) NOT NULL,
    Prenom VARCHAR(100),
    Specialite VARCHAR(100),
    AdressePostale VARCHAR(255),
    Email VARCHAR(100)
);

-- Consultations table
CREATE TABLE Consult_prob (
    ID_probleme SERIAL PRIMARY KEY,
    ID_patient INT NOT NULL,
    ID_medecin INT,
    DateConsultation TIMESTAMP,
    LibelleProbleme VARCHAR(255),
    FOREIGN KEY (ID_patient) REFERENCES Patients(ID_patient),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (ID_medecin) REFERENCES Medecins(ID_medecin)
);

-- Motifs table
CREATE TABLE Motifs (
    ID_motif SERIAL PRIMARY KEY,
    Libelle VARCHAR(255) NOT NULL,
    ID_probleme INT NOT NULL,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Symptomes table
CREATE TABLE Symptomes (
    ID_symptome SERIAL PRIMARY KEY,
    Libelle VARCHAR(255) NOT NULL,
    ID_probleme INT NOT NULL,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Observations table
CREATE TABLE Observations (
    ID_observation SERIAL PRIMARY KEY,
    Observation TEXT NOT NULL,
    DateObservation TIMESTAMP,
    ID_probleme INT NOT NULL,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Antecedents table
CREATE TABLE Antecedents (
    ID_ATCD SERIAL PRIMARY KEY,
    Libelle VARCHAR(255) NOT NULL,
    Details TEXT,
    ID_patient INT NOT NULL,
    FOREIGN KEY (ID_patient) REFERENCES Patients(ID_patient)
);

-- Biometrie table
CREATE TABLE Biometrie (
    ID_biometrie SERIAL PRIMARY KEY,
    ID_patient INT NOT NULL,
    ID_probleme INT,
    DateMesure TIMESTAMP NOT NULL,
    Temperature DECIMAL(4,1),
    TensionSystolique INT,
    TensionDiastolique INT,
    Pouls INT,
    SpO2 INT,
    FrequenceResp INT,
    Commentaire TEXT,
    FOREIGN KEY (ID_patient) REFERENCES Patients(ID_patient),
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Diagnostics table
CREATE TABLE Diagnostics (
    ID_diagnostic SERIAL PRIMARY KEY,
    CodeCIM VARCHAR(20),
    Libelle VARCHAR(255) NOT NULL,
    ID_probleme INT NOT NULL,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Examens complémentaires table
CREATE TABLE Examens_comp (
    ID_examen SERIAL PRIMARY KEY,
    TypeExamen VARCHAR(100),
    Libelle VARCHAR(255),
    DateExamen TIMESTAMP,
    Resultats TEXT,
    ID_probleme INT NOT NULL,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Procedures soins table
CREATE TABLE Procedures_soins (
    ID_procedure SERIAL PRIMARY KEY,
    Libelle VARCHAR(255) NOT NULL,
    TypeProcedure VARCHAR(100),
    DateProcedure TIMESTAMP,
    ID_probleme INT NOT NULL,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
);

-- Livret medicaments table
CREATE TABLE Livret_medicaments (
    ID_livret SERIAL PRIMARY KEY,
    CIP13 VARCHAR(20),
    DCI VARCHAR(255),
    NomCommercial VARCHAR(255)
);

-- Medicaments table
CREATE TABLE Medicaments (
    ID_medicament SERIAL PRIMARY KEY,
    ID_livret INT,
    Nom VARCHAR(255) NOT NULL,
    Forme VARCHAR(100),
    Dosage VARCHAR(100),
    FOREIGN KEY (ID_livret) REFERENCES Livret_medicaments(ID_livret)
);

-- Prescriptions table
CREATE TABLE Prescriptions (
    ID_prescription SERIAL PRIMARY KEY,
    ID_probleme INT,
    ID_medicament INT,
    DatePrescription TIMESTAMP,
    FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme),
    FOREIGN KEY (ID_medicament) REFERENCES Medicaments(ID_medicament)
);
