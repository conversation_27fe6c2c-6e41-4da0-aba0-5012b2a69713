# Hospital Data Sync Script

This directory contains scripts for syncing data from the hospital database to the app database.

## sync_hospital_data.py

This script syncs data from the hospital database to the app database and creates users with the specified username format.

### What it does:

1. **Creates Departments**: Extracts unique doctor specialties from the `Medecins` table and creates departments in the app database
2. **Creates Roles**: Sets up basic roles (admin, head_of_department, doctor) - Note: doctors and nurses share the same "doctor" role
3. **Creates Doctor Users**: Creates users from the `Medecins` table with username format `{lastname}{firstname[0]}{number}`
4. **Creates Admin User**: Creates a system administrator user

### Username Format

Users are created with the format: `{lastname}{firstname[0]}{number}`

Examples:
- Doctor: Nom="Dupont", Prenom="<PERSON>" → username="dupontj1"
- Doctor: Nom="<PERSON>", Prenom="<PERSON>" → username="martinm1"
- If username exists, increment number: "dupontj2", "dupontj3", etc.

### Default Passwords

The script creates users with default passwords:
- **Admin user**: username=`admin`, password=`admin123`
- **Doctor users**: password=`doctor123`

⚠️ **Important**: Change these default passwords in production!

### Configuration

**Two Database Setup:**

1. Open `scripts/sync_hospital_data.py`
2. Find the configuration section at the top:

```python
# Option 1: Use separate databases
USE_SEPARATE_DATABASES = True

if USE_SEPARATE_DATABASES:
    # Hospital database connection
    HOSPITAL_DSN = "***********************************************/hospital_db"

    # App database connection
    APP_DSN = "******************************************/app_db"
```

3. Set `USE_SEPARATE_DATABASES = True`
4. Update `HOSPITAL_DSN` with your hospital database connection string
5. Update `APP_DSN` with your app database connection string

**Single Database Setup:**

1. Set `USE_SEPARATE_DATABASES = False`
2. The script will use the configuration from `backend/config.py`

### Usage

Run the script from the project root:

```bash
cd /path/to/your/project
python scripts/sync_hospital_data.py
```

### Prerequisites

- Python 3.7+
- psycopg2 (PostgreSQL adapter)
- passlib (for password hashing)
- Access to both hospital and app databases

### Database Requirements

**Hospital Database:**
- Tables: `Medecins` (with columns: ID_medecin, Nom, Prenom, Specialite, Email)
- Contains the source data for doctors and specialties

**App Database:**
- Tables: `department`, `role`, `user` (permissions will be handled separately later)
- Will be populated with synced data from hospital database

**Connection Options:**
- **Separate Databases**: Configure different connection strings for hospital and app databases
- **Same Database**: Use the same database for both (configure in `backend/config.py`)
- **Connection Pooling**: Automatically manages database connections for better performance

### Logging

The script creates a log file at `scripts/sync_log.txt` with detailed information about the sync process.

### Safety Features

- **Idempotent**: Safe to run multiple times without creating duplicates
- **Transaction-based**: Uses database transactions for data consistency
- **Conflict handling**: Uses `ON CONFLICT DO NOTHING` to avoid duplicate entries
- **Error handling**: Comprehensive error handling with rollback on failures

### Troubleshooting

1. **Connection errors**: Check your database configuration in `backend/config.py`
2. **Permission errors**: Ensure the database user has CREATE, INSERT, UPDATE permissions
3. **Missing tables**: Ensure both hospital and app database schemas are properly set up
4. **Duplicate usernames**: The script automatically handles username conflicts by incrementing numbers

### Example Output

```
2024-01-15 10:30:00 - INFO - Starting hospital data sync...
2024-01-15 10:30:01 - INFO - Successfully connected to both hospital and app databases
2024-01-15 10:30:01 - INFO - Syncing departments...
2024-01-15 10:30:01 - INFO - Created 8 departments
2024-01-15 10:30:02 - INFO - Syncing roles...
2024-01-15 10:30:02 - INFO - Created 3 roles
2024-01-15 10:30:03 - INFO - Syncing users from doctors...
2024-01-15 10:30:04 - INFO - Created 25 doctor users
2024-01-15 10:30:05 - INFO - Creating admin user...
2024-01-15 10:30:07 - INFO - Admin user created successfully
2024-01-15 10:30:07 - INFO - Sync completed successfully in 7.23 seconds

==================================================
SYNC COMPLETED SUCCESSFULLY!
==================================================

Roles created:
- admin: System administrator
- head_of_department: Head of medical department
- doctor: Medical doctor and nursing staff

Default passwords created:
- Admin user: username='admin', password='admin123'
- Doctor users: password='doctor123'

Please change these default passwords in production!
==================================================
```
