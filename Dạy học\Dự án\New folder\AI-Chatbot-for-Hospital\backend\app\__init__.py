"""
Legacy Flask application - converted to FastAPI
This file is kept for compatibility but the main application now uses FastAPI
"""

# Global variables for compatibility
vn = None
cache = None

def create_app(config=None):
    """Legacy Flask app creation - not used in FastAPI version"""
    raise NotImplementedError("This application has been converted to FastAPI. Use main.py instead.")

def get_vanna():
    """Get the global Vanna instance"""
    return vn

def get_cache():
    """Get the global cache instance"""
    return cache
