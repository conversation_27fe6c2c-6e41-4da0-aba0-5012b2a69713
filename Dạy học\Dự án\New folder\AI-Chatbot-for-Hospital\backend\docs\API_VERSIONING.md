# API Versioning Strategy

## Overview

The Hospital Assistant System API implements comprehensive versioning to ensure backward compatibility, clear migration paths, and future-proof development.

## Versioning Scheme

### Current Versions

- **V1 (Stable)**: `/api/v1/` - Current stable API version
- **V0 (Legacy)**: `/api/v0/` and legacy paths - Maintained for backward compatibility

### Version Headers

All API responses include version information in headers:

```
X-API-Version: 1.1.0
X-Endpoint-Version: v1
X-Version-Status: stable
```

Legacy endpoints include deprecation warnings:
```
X-Deprecation-Warning: This endpoint version is legacy. Please migrate to v1 endpoints.
```

## API Endpoints by Version

### V1 Endpoints (Recommended)

All V1 endpoints are prefixed with `/api/v1/` and follow a consistent structure:

#### Health & System
- `GET /api/v1/health` - Health check
- `GET /api/v1/version` - API version information  
- `GET /api/v1/endpoints` - Complete endpoint documentation

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user information

#### SQL Generation & Execution
- `GET /api/v1/sql/generate_sql` - Generate SQL from natural language
- `GET /api/v1/sql/run_sql` - Execute SQL query

#### Data Analysis
- `GET /api/v1/analysis/generate_summary_stream` - Generate streaming data summary
- `GET /api/v1/analysis/generate_plotly_figure` - Generate chart visualization

#### Dashboard & Statistics
- `GET /api/v1/dashboard/test` - Dashboard API test
- `GET /api/v1/dashboard/patients` - Patient statistics
- `GET /api/v1/dashboard/consultations` - Consultation statistics
- `GET /api/v1/dashboard/doctors` - Doctor statistics
- `GET /api/v1/dashboard/patient-demographics` - Patient demographics

#### Search & Details
- `POST /api/v1/search/patients` - Search patients
- `POST /api/v1/search/consultations` - Search consultations
- `POST /api/v1/search/doctors` - Search doctors
- `GET /api/v1/search/patients/{patient_id}/details` - Patient details
- `GET /api/v1/search/consultations/{consultation_id}/details` - Consultation details
- `GET /api/v1/search/doctors/{doctor_id}/details` - Doctor details
- `GET /api/v1/search/test` - Search API test

### V0 Endpoints (Legacy)

Legacy endpoints maintained for backward compatibility:

#### Health & Authentication
- `GET /health` - Legacy health check
- `POST /auth/login` - Legacy login
- `POST /auth/logout` - Legacy logout
- `GET /auth/me` - Legacy user info

#### Other Legacy Endpoints
- `GET /api/v0/*` - All other legacy endpoints under v0 prefix

## Migration Guide

### From V0 to V1

| V0 Endpoint | V1 Equivalent |
|-------------|---------------|
| `GET /health` | `GET /api/v1/health` |
| `POST /auth/login` | `POST /api/v1/auth/login` |
| `POST /auth/logout` | `POST /api/v1/auth/logout` |
| `GET /auth/me` | `GET /api/v1/auth/me` |
| `GET /api/v0/generate_sql` | `GET /api/v1/sql/generate_sql` |
| `GET /api/v0/run_sql` | `GET /api/v1/sql/run_sql` |
| `GET /api/v0/generate_summary_stream` | `GET /api/v1/analysis/generate_summary_stream` |
| `GET /api/v0/generate_plotly_figure` | `GET /api/v1/analysis/generate_plotly_figure` |
| `GET /api/v0/dashboard/*` | `GET /api/v1/dashboard/*` |
| `GET /api/v0/search/*` | `GET /api/v1/search/*` |

### Migration Steps

1. **Update Base URLs**: Change all API calls to use `/api/v1/` prefix
2. **Update Authentication**: Use `/api/v1/auth/` endpoints
3. **Update Health Checks**: Use `/api/v1/health`
4. **Test Thoroughly**: Verify all functionality works with V1 endpoints
5. **Monitor Headers**: Check for deprecation warnings in responses

## Response Format

### V1 Response Structure

All V1 responses include metadata:

```json
{
  "status": "success",
  "data": { ... },
  "_meta": {
    "api_version": "1.1.0",
    "endpoint_version": "v1",
    "version_status": "stable",
    "is_legacy": false,
    "timestamp": "2025-07-01T12:00:00Z"
  }
}
```

### Legacy Response Structure

Legacy responses include migration hints:

```json
{
  "status": "success", 
  "data": { ... },
  "_meta": {
    "api_version": "1.1.0",
    "endpoint_version": "v0",
    "version_status": "legacy",
    "is_legacy": true,
    "migration_hint": "Consider migrating to v1 equivalent: /api/v1/..."
  }
}
```

## Best Practices

### For New Development
- Always use V1 endpoints (`/api/v1/`)
- Check response headers for version information
- Handle version metadata in responses appropriately

### For Existing Applications
- Plan migration from V0 to V1 endpoints
- Monitor deprecation warnings in response headers
- Test V1 endpoints in staging before production migration

### For API Consumers
- Include version information in error reporting
- Implement graceful handling of version changes
- Monitor for new API versions and deprecation notices

## Future Versioning

### Version Lifecycle
1. **Development**: New features developed in next version
2. **Beta**: Pre-release testing with selected users
3. **Stable**: General availability for production use
4. **Legacy**: Maintained for backward compatibility
5. **Deprecated**: Scheduled for removal (with advance notice)

### Deprecation Policy
- Legacy versions maintained for minimum 6 months after replacement
- Advance notice provided before deprecation
- Clear migration documentation and tools provided
- Gradual deprecation with warnings before removal

## Support

For questions about API versioning:
- Check `/api/v1/version` for current version information
- Review `/api/v1/endpoints` for complete endpoint documentation
- Monitor response headers for version and deprecation information
